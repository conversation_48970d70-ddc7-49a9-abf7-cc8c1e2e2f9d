package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.MonthlyDiscountFactorDTO;
import com.xl.alm.app.dto.TermDataDTO;
import com.xl.alm.app.query.MonthlyDiscountFactorQuery;
import com.xl.alm.app.service.MonthlyDiscountFactorService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.TermDataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 月度折现因子表含价差 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/monthly/discount/factor")
public class MonthlyDiscountFactorController extends BaseController {

    @Autowired
    private MonthlyDiscountFactorService monthlyDiscountFactorService;

    /**
     * 查询月度折现因子含价差列表
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonthlyDiscountFactorQuery monthlyDiscountFactorQuery) {
        startPage();
        List<MonthlyDiscountFactorDTO> list = monthlyDiscountFactorService.selectMonthlyDiscountFactorDtoList(monthlyDiscountFactorQuery);
        return getDataTable(list);
    }

    /**
     * 获取月度折现因子含价差详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(monthlyDiscountFactorService.selectMonthlyDiscountFactorDtoById(id));
    }

    /**
     * 新增月度折现因子含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:add')")
    @Log(title = "月度折现因子含价差", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody MonthlyDiscountFactorDTO monthlyDiscountFactorDto) {
        return toAjax(monthlyDiscountFactorService.insertMonthlyDiscountFactorDto(monthlyDiscountFactorDto));
    }

    /**
     * 修改月度折现因子含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:edit')")
    @Log(title = "月度折现因子含价差", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody MonthlyDiscountFactorDTO monthlyDiscountFactorDto) {
        return toAjax(monthlyDiscountFactorService.updateMonthlyDiscountFactorDto(monthlyDiscountFactorDto));
    }

    /**
     * 删除月度折现因子含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:remove')")
    @Log(title = "月度折现因子含价差", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(monthlyDiscountFactorService.deleteMonthlyDiscountFactorDtoByIds(ids));
    }

    /**
     * 导出月度折现因子含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:export')")
    @Log(title = "月度折现因子含价差", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonthlyDiscountFactorQuery monthlyDiscountFactorQuery) {
        List<MonthlyDiscountFactorDTO> list = monthlyDiscountFactorService.selectMonthlyDiscountFactorDtoList(monthlyDiscountFactorQuery);
        ExcelUtil<MonthlyDiscountFactorDTO> util = new ExcelUtil<>(MonthlyDiscountFactorDTO.class);
        util.exportExcel(list, "月度折现因子含价差数据", response);
    }

    /**
     * 获取月度折现因子含价差导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<MonthlyDiscountFactorDTO> util = new ExcelUtil<>(MonthlyDiscountFactorDTO.class);
        util.exportTemplateExcel(response, "月度折现因子含价差数据");
    }

    /**
     * 导入月度折现因子含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:import')")
    @Log(title = "月度折现因子含价差", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<MonthlyDiscountFactorDTO> util = new ExcelUtil<>(MonthlyDiscountFactorDTO.class);
        List<MonthlyDiscountFactorDTO> monthlyDiscountFactorList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = monthlyDiscountFactorService.importMonthlyDiscountFactorDto(monthlyDiscountFactorList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 获取期限数据列表
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:query')")
    @GetMapping("/termData/{id}")
    public Result getTermData(@PathVariable("id") Long id) {
        MonthlyDiscountFactorDTO dto = monthlyDiscountFactorService.selectMonthlyDiscountFactorDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        List<TermDataDTO> termDataList = TermDataUtil.parseJsonToTermDataList(dto.getMonthlyDiscountFactorSet());
        return Result.success(termDataList);
    }

    /**
     * 更新期限数据
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:edit')")
    @Log(title = "月度折现因子期限数据", businessType = BusinessType.UPDATE)
    @PutMapping("/termData/{id}")
    public Result updateTermData(@PathVariable("id") Long id, @RequestBody List<TermDataDTO> termDataList) {
        // 验证期限数据完整性
        if (!TermDataUtil.validateTermDataCompleteness(termDataList)) {
            return Result.error("期限数据不完整，必须包含term_0到term_600共601个期限");
        }

        // 获取原数据
        MonthlyDiscountFactorDTO dto = monthlyDiscountFactorService.selectMonthlyDiscountFactorDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        // 转换期限数据为JSON
        String jsonStr = TermDataUtil.convertTermDataListToJson(termDataList);
        dto.setMonthlyDiscountFactorSet(jsonStr);
        dto.setUpdateBy(getUsername());

        return toAjax(monthlyDiscountFactorService.updateMonthlyDiscountFactorDto(dto));
    }
}
