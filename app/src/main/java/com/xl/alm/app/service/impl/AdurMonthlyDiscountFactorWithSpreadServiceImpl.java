package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AdurMonthlyDiscountFactorWithSpreadDTO;
import com.xl.alm.app.entity.AdurMonthlyDiscountFactorWithSpreadEntity;
import com.xl.alm.app.mapper.AdurMonthlyDiscountFactorWithSpreadMapper;
import com.xl.alm.app.query.AdurMonthlyDiscountFactorWithSpreadQuery;
import com.xl.alm.app.service.AdurMonthlyDiscountFactorWithSpreadService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ADUR月度折现因子表含价差 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurMonthlyDiscountFactorWithSpreadServiceImpl implements AdurMonthlyDiscountFactorWithSpreadService {

    @Autowired
    private AdurMonthlyDiscountFactorWithSpreadMapper adurMonthlyDiscountFactorWithSpreadMapper;

    /**
     * 查询ADUR月度折现因子表含价差列表
     *
     * @param adurMonthlyDiscountFactorWithSpreadQuery ADUR月度折现因子表含价差查询条件
     * @return ADUR月度折现因子表含价差列表
     */
    @Override
    public List<AdurMonthlyDiscountFactorWithSpreadDTO> selectAdurMonthlyDiscountFactorWithSpreadDtoList(AdurMonthlyDiscountFactorWithSpreadQuery adurMonthlyDiscountFactorWithSpreadQuery) {
        List<AdurMonthlyDiscountFactorWithSpreadEntity> entityList = adurMonthlyDiscountFactorWithSpreadMapper.selectAdurMonthlyDiscountFactorWithSpreadEntityList(adurMonthlyDiscountFactorWithSpreadQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AdurMonthlyDiscountFactorWithSpreadDTO.class);
    }

    /**
     * 用id查询ADUR月度折现因子表含价差
     *
     * @param id id
     * @return ADUR月度折现因子表含价差
     */
    @Override
    public AdurMonthlyDiscountFactorWithSpreadDTO selectAdurMonthlyDiscountFactorWithSpreadDtoById(Long id) {
        AdurMonthlyDiscountFactorWithSpreadEntity entity = adurMonthlyDiscountFactorWithSpreadMapper.selectAdurMonthlyDiscountFactorWithSpreadEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurMonthlyDiscountFactorWithSpreadDTO.class);
    }

    /**
     * 根据账期、资产编号、久期类型和基点类型查询ADUR月度折现因子表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param durationType 久期类型
     * @param basisPointType 基点类型
     * @return ADUR月度折现因子表含价差
     */
    @Override
    public AdurMonthlyDiscountFactorWithSpreadDTO selectAdurMonthlyDiscountFactorWithSpreadDtoByCondition(String accountPeriod, String assetNumber, String durationType, String basisPointType) {
        AdurMonthlyDiscountFactorWithSpreadEntity entity = adurMonthlyDiscountFactorWithSpreadMapper.selectAdurMonthlyDiscountFactorWithSpreadEntityByCondition(accountPeriod, assetNumber, durationType, basisPointType);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurMonthlyDiscountFactorWithSpreadDTO.class);
    }

    /**
     * 新增ADUR月度折现因子表含价差
     *
     * @param adurMonthlyDiscountFactorWithSpreadDTO ADUR月度折现因子表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdurMonthlyDiscountFactorWithSpreadDto(AdurMonthlyDiscountFactorWithSpreadDTO adurMonthlyDiscountFactorWithSpreadDTO) {
        AdurMonthlyDiscountFactorWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(adurMonthlyDiscountFactorWithSpreadDTO, AdurMonthlyDiscountFactorWithSpreadEntity.class);
        return adurMonthlyDiscountFactorWithSpreadMapper.insertAdurMonthlyDiscountFactorWithSpreadEntity(entity);
    }

    /**
     * 批量插入ADUR月度折现因子表含价差数据
     *
     * @param adurMonthlyDiscountFactorWithSpreadDtoList ADUR月度折现因子表含价差列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertAdurMonthlyDiscountFactorWithSpreadDto(List<AdurMonthlyDiscountFactorWithSpreadDTO> adurMonthlyDiscountFactorWithSpreadDtoList) {
        if (adurMonthlyDiscountFactorWithSpreadDtoList == null || adurMonthlyDiscountFactorWithSpreadDtoList.isEmpty()) {
            return 0;
        }
        List<AdurMonthlyDiscountFactorWithSpreadEntity> entityList = EntityDtoConvertUtil.convertToEntityList(adurMonthlyDiscountFactorWithSpreadDtoList, AdurMonthlyDiscountFactorWithSpreadEntity.class);
        return adurMonthlyDiscountFactorWithSpreadMapper.batchInsertAdurMonthlyDiscountFactorWithSpreadEntity(entityList);
    }

    /**
     * 更新ADUR月度折现因子表含价差数据
     *
     * @param dto ADUR月度折现因子表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdurMonthlyDiscountFactorWithSpreadDto(AdurMonthlyDiscountFactorWithSpreadDTO dto) {
        AdurMonthlyDiscountFactorWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(dto, AdurMonthlyDiscountFactorWithSpreadEntity.class);
        return adurMonthlyDiscountFactorWithSpreadMapper.updateAdurMonthlyDiscountFactorWithSpreadEntity(entity);
    }

    /**
     * 删除指定id的ADUR月度折现因子表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteAdurMonthlyDiscountFactorWithSpreadDtoById(Long id) {
        return adurMonthlyDiscountFactorWithSpreadMapper.deleteAdurMonthlyDiscountFactorWithSpreadEntityById(id);
    }

    /**
     * 批量删除ADUR月度折现因子表含价差
     *
     * @param ids 需要删除的ADUR月度折现因子表含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurMonthlyDiscountFactorWithSpreadDtoByIds(Long[] ids) {
        return adurMonthlyDiscountFactorWithSpreadMapper.deleteAdurMonthlyDiscountFactorWithSpreadEntityByIds(ids);
    }

    /**
     * 根据账期删除ADUR月度折现因子表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurMonthlyDiscountFactorWithSpreadDtoByAccountPeriod(String accountPeriod) {
        return adurMonthlyDiscountFactorWithSpreadMapper.deleteAdurMonthlyDiscountFactorWithSpreadEntityByAccountPeriod(accountPeriod);
    }

    /**
     * 导入ADUR月度折现因子表含价差
     *
     * @param dtoList       ADUR月度折现因子表含价差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importAdurMonthlyDiscountFactorWithSpreadDto(List<AdurMonthlyDiscountFactorWithSpreadDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.size() == 0) {
            throw new RuntimeException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AdurMonthlyDiscountFactorWithSpreadDTO dto : dtoList) {
            try {
                // 验证是否存在这个数据
                AdurMonthlyDiscountFactorWithSpreadDTO existDto = this.selectAdurMonthlyDiscountFactorWithSpreadDtoByCondition(dto.getAccountPeriod(), dto.getAssetNumber(), dto.getDurationType(), dto.getBasisPointType());
                if (StringUtils.isNull(existDto)) {
                    dto.setCreateBy(username);
                    this.insertAdurMonthlyDiscountFactorWithSpreadDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 久期类型 " + dto.getDurationType() + " 基点类型 " + dto.getBasisPointType() + " 导入成功");
                } else if (updateSupport) {
                    dto.setUpdateBy(username);
                    dto.setId(existDto.getId());
                    this.updateAdurMonthlyDiscountFactorWithSpreadDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 久期类型 " + dto.getDurationType() + " 基点类型 " + dto.getBasisPointType() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 久期类型 " + dto.getDurationType() + " 基点类型 " + dto.getBasisPointType() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 久期类型 " + dto.getDurationType() + " 基点类型 " + dto.getBasisPointType() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
