package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.xl.alm.app.dto.DurationAssetSummaryDTO;
import com.xl.alm.app.query.DurationAssetSummaryQuery;
import com.xl.alm.app.service.DurationAssetSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 久期资产结果汇总表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/duration/asset/summary")
public class DurationAssetSummaryController extends BaseController {

    @Autowired
    private DurationAssetSummaryService durationAssetSummaryService;

    /**
     * 查询久期资产结果汇总表列表
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:summary:list')")
    @GetMapping("/list")
    public TableDataInfo list(DurationAssetSummaryQuery durationAssetSummaryQuery) {
        startPage();
        List<DurationAssetSummaryDTO> list = durationAssetSummaryService.selectDurationAssetSummaryDtoList(durationAssetSummaryQuery);
        return getDataTable(list);
    }

    /**
     * 导出久期资产结果汇总表列表
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:summary:export')")
    @Log(title = "久期资产结果汇总表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DurationAssetSummaryQuery durationAssetSummaryQuery) {
        List<DurationAssetSummaryDTO> list = durationAssetSummaryService.selectDurationAssetSummaryDtoList(durationAssetSummaryQuery);
        com.xl.alm.app.util.ExcelUtil<DurationAssetSummaryDTO> util = new com.xl.alm.app.util.ExcelUtil<>(DurationAssetSummaryDTO.class);
        util.exportExcel(list, "久期资产结果汇总表数据", response);
    }

    /**
     * 获取久期资产结果汇总表详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:summary:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(durationAssetSummaryService.selectDurationAssetSummaryDtoById(id));
    }

    /**
     * 新增久期资产结果汇总表
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:summary:add')")
    @Log(title = "久期资产结果汇总表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DurationAssetSummaryDTO durationAssetSummaryDTO) {
        return toAjax(durationAssetSummaryService.insertDurationAssetSummaryDto(durationAssetSummaryDTO));
    }

    /**
     * 修改久期资产结果汇总表
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:summary:edit')")
    @Log(title = "久期资产结果汇总表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DurationAssetSummaryDTO durationAssetSummaryDTO) {
        return toAjax(durationAssetSummaryService.updateDurationAssetSummaryDto(durationAssetSummaryDTO));
    }

    /**
     * 删除久期资产结果汇总表
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:summary:remove')")
    @Log(title = "久期资产结果汇总表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(durationAssetSummaryService.deleteDurationAssetSummaryDtoByIds(ids));
    }

    /**
     * 获取久期资产结果汇总表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        com.xl.alm.app.util.ExcelUtil<DurationAssetSummaryDTO> util = new com.xl.alm.app.util.ExcelUtil<>(DurationAssetSummaryDTO.class);
        util.exportTemplateExcel(response, "久期资产结果汇总表");
    }

    /**
     * 导入久期资产结果汇总表数据
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:summary:import')")
    @Log(title = "久期资产结果汇总表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        com.xl.alm.app.util.ExcelUtil<DurationAssetSummaryDTO> util = new com.xl.alm.app.util.ExcelUtil<>(DurationAssetSummaryDTO.class);
        List<DurationAssetSummaryDTO> durationAssetSummaryList = util.importExcel(file.getInputStream());
        String username = getUsername();
        String message = durationAssetSummaryService.importDurationAssetSummaryDto(durationAssetSummaryList, updateSupport, username);
        return Result.success(message);
    }
}
