package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.AnnualDiscountCurveDTO;
import com.xl.alm.app.dto.TermDataDTO;
import com.xl.alm.app.query.AnnualDiscountCurveQuery;
import com.xl.alm.app.service.AnnualDiscountCurveService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.TermDataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 年度折现曲线表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/annual/discount/curve")
public class AnnualDiscountCurveController extends BaseController {

    @Autowired
    private AnnualDiscountCurveService annualDiscountCurveService;

    /**
     * 查询年度折现曲线列表
     */
    @PreAuthorize("@ss.hasPermi('adur:annual:discount:curve:list')")
    @GetMapping("/list")
    public TableDataInfo list(AnnualDiscountCurveQuery annualDiscountCurveQuery) {
        startPage();
        List<AnnualDiscountCurveDTO> list = annualDiscountCurveService.selectAnnualDiscountCurveDtoList(annualDiscountCurveQuery);
        return getDataTable(list);
    }

    /**
     * 获取年度折现曲线详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:annual:discount:curve:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(annualDiscountCurveService.selectAnnualDiscountCurveDtoById(id));
    }

    /**
     * 新增年度折现曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:annual:discount:curve:add')")
    @Log(title = "年度折现曲线", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody AnnualDiscountCurveDTO annualDiscountCurveDto) {
        return toAjax(annualDiscountCurveService.insertAnnualDiscountCurveDto(annualDiscountCurveDto));
    }

    /**
     * 修改年度折现曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:annual:discount:curve:edit')")
    @Log(title = "年度折现曲线", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody AnnualDiscountCurveDTO annualDiscountCurveDto) {
        return toAjax(annualDiscountCurveService.updateAnnualDiscountCurveDto(annualDiscountCurveDto));
    }

    /**
     * 删除年度折现曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:annual:discount:curve:remove')")
    @Log(title = "年度折现曲线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(annualDiscountCurveService.deleteAnnualDiscountCurveDtoByIds(ids));
    }

    /**
     * 导出年度折现曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:annual:discount:curve:export')")
    @Log(title = "年度折现曲线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnualDiscountCurveQuery annualDiscountCurveQuery) {
        List<AnnualDiscountCurveDTO> list = annualDiscountCurveService.selectAnnualDiscountCurveDtoList(annualDiscountCurveQuery);
        ExcelUtil<AnnualDiscountCurveDTO> util = new ExcelUtil<>(AnnualDiscountCurveDTO.class);
        util.exportExcel(list, "年度折现曲线数据", response);
    }

    /**
     * 获取年度折现曲线导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AnnualDiscountCurveDTO> util = new ExcelUtil<>(AnnualDiscountCurveDTO.class);
        util.exportTemplateExcel(response, "年度折现曲线数据");
    }

    /**
     * 导入年度折现曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:annual:discount:curve:import')")
    @Log(title = "年度折现曲线", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AnnualDiscountCurveDTO> util = new ExcelUtil<>(AnnualDiscountCurveDTO.class);
        List<AnnualDiscountCurveDTO> annualDiscountCurveList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = annualDiscountCurveService.importAnnualDiscountCurveDto(annualDiscountCurveList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 获取期限数据列表
     */
    @PreAuthorize("@ss.hasPermi('adur:annual:discount:curve:query')")
    @GetMapping("/termData/{id}")
    public Result getTermData(@PathVariable("id") Long id) {
        AnnualDiscountCurveDTO dto = annualDiscountCurveService.selectAnnualDiscountCurveDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        List<TermDataDTO> termDataList = convertDtoToTermDataList(dto);
        return Result.success(termDataList);
    }

    /**
     * 更新期限数据
     */
    @PreAuthorize("@ss.hasPermi('adur:annual:discount:curve:edit')")
    @Log(title = "年度折现曲线期限数据", businessType = BusinessType.UPDATE)
    @PutMapping("/termData/{id}")
    public Result updateTermData(@PathVariable("id") Long id, @RequestBody List<TermDataDTO> termDataList) {
        // 验证期限数据完整性（年度折现曲线只有51个期限）
        if (termDataList == null || termDataList.size() != 51) {
            return Result.error("期限数据不完整，必须包含term_0到term_50共51个期限");
        }

        // 获取原数据
        AnnualDiscountCurveDTO dto = annualDiscountCurveService.selectAnnualDiscountCurveDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        // 转换期限数据到DTO字段
        convertTermDataListToDto(termDataList, dto);
        dto.setUpdateBy(getUsername());

        return toAjax(annualDiscountCurveService.updateAnnualDiscountCurveDto(dto));
    }

    /**
     * 将DTO转换为期限数据列表
     */
    private List<TermDataDTO> convertDtoToTermDataList(AnnualDiscountCurveDTO dto) {
        List<TermDataDTO> termDataList = new ArrayList<>();

        // 使用反射获取所有term字段
        java.lang.reflect.Field[] fields = dto.getClass().getDeclaredFields();
        for (java.lang.reflect.Field field : fields) {
            if (field.getName().startsWith("term") && !field.getName().equals("termDataList")) {
                try {
                    field.setAccessible(true);
                    // 从字段名提取期限编号
                    String fieldName = field.getName(); // 如：term0, term1, term2...
                    Integer termNumber = Integer.parseInt(fieldName.replace("term", ""));
                    BigDecimal value = (BigDecimal) field.get(dto);

                    TermDataDTO termData = new TermDataDTO(termNumber, value != null ? value : BigDecimal.ZERO);
                    termDataList.add(termData);
                } catch (IllegalAccessException | NumberFormatException e) {
                    // 忽略访问异常或数字格式异常
                }
            }
        }

        // 按期限编号排序
        termDataList.sort((a, b) -> Integer.compare(a.getTermNumber(), b.getTermNumber()));

        return termDataList;
    }

    /**
     * 将期限数据列表转换到DTO字段
     */
    private void convertTermDataListToDto(List<TermDataDTO> termDataList, AnnualDiscountCurveDTO dto) {
        for (TermDataDTO termData : termDataList) {
            // 根据期限编号构造字段名
            String fieldName = "term" + termData.getTermNumber(); // 如：term0, term1, term2...
            try {
                java.lang.reflect.Field field = dto.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                field.set(dto, termData.getTermValue());
            } catch (NoSuchFieldException | IllegalAccessException e) {
                // 忽略字段不存在或访问异常
            }
        }
    }
}
