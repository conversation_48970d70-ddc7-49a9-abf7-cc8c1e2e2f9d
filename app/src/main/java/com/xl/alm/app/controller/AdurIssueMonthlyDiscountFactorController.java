package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.AdurIssueMonthlyDiscountFactorDTO;
import com.xl.alm.app.dto.TermDataDTO;
import com.xl.alm.app.query.AdurIssueMonthlyDiscountFactorQuery;
import com.xl.alm.app.service.AdurIssueMonthlyDiscountFactorService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.TermDataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * ADUR关键久期折现因子表含价差(第二个)Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/issue/monthly/discount/factor")
public class AdurIssueMonthlyDiscountFactorController extends BaseController {

    @Autowired
    private AdurIssueMonthlyDiscountFactorService adurIssueMonthlyDiscountFactorService;

    /**
     * 查询ADUR关键久期折现因子表含价差(第二个)列表
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdurIssueMonthlyDiscountFactorQuery adurIssueMonthlyDiscountFactorQuery) {
        startPage();
        List<AdurIssueMonthlyDiscountFactorDTO> list = adurIssueMonthlyDiscountFactorService.selectAdurIssueMonthlyDiscountFactorDtoList(adurIssueMonthlyDiscountFactorQuery);
        return getDataTable(list);
    }

    /**
     * 导出ADUR关键久期折现因子表含价差(第二个)列表
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:export')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdurIssueMonthlyDiscountFactorQuery adurIssueMonthlyDiscountFactorQuery) {
        List<AdurIssueMonthlyDiscountFactorDTO> list = adurIssueMonthlyDiscountFactorService.selectAdurIssueMonthlyDiscountFactorDtoList(adurIssueMonthlyDiscountFactorQuery);
        ExcelUtil<AdurIssueMonthlyDiscountFactorDTO> util = new ExcelUtil<AdurIssueMonthlyDiscountFactorDTO>(AdurIssueMonthlyDiscountFactorDTO.class);
        util.exportExcel(list, "ADUR关键久期折现因子表含价差(第二个)数据", response);
    }

    /**
     * 获取ADUR关键久期折现因子表含价差(第二个)详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(adurIssueMonthlyDiscountFactorService.selectAdurIssueMonthlyDiscountFactorDtoById(id));
    }

    /**
     * 新增ADUR关键久期折现因子表含价差(第二个)
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:add')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AdurIssueMonthlyDiscountFactorDTO adurIssueMonthlyDiscountFactorDTO) {
        adurIssueMonthlyDiscountFactorDTO.setCreateBy(getUsername());
        return toAjax(adurIssueMonthlyDiscountFactorService.insertAdurIssueMonthlyDiscountFactorDto(adurIssueMonthlyDiscountFactorDTO));
    }

    /**
     * 修改ADUR关键久期折现因子表含价差(第二个)
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:edit')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AdurIssueMonthlyDiscountFactorDTO adurIssueMonthlyDiscountFactorDTO) {
        adurIssueMonthlyDiscountFactorDTO.setUpdateBy(getUsername());
        return toAjax(adurIssueMonthlyDiscountFactorService.updateAdurIssueMonthlyDiscountFactorDto(adurIssueMonthlyDiscountFactorDTO));
    }

    /**
     * 删除ADUR关键久期折现因子表含价差(第二个)
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:remove')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(adurIssueMonthlyDiscountFactorService.deleteAdurIssueMonthlyDiscountFactorDtoByIds(ids));
    }

    /**
     * 删除指定账期的ADUR关键久期折现因子表含价差(第二个)
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:remove')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountPeriod}")
    public Result removeByPeriod(@PathVariable String accountPeriod) {
        return toAjax(adurIssueMonthlyDiscountFactorService.deleteAdurIssueMonthlyDiscountFactorDtoByAccountPeriod(accountPeriod));
    }

    /**
     * 获取ADUR关键久期折现因子表含价差(第二个)导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AdurIssueMonthlyDiscountFactorDTO> util = new ExcelUtil<AdurIssueMonthlyDiscountFactorDTO>(AdurIssueMonthlyDiscountFactorDTO.class);
        util.exportTemplateExcel(response, "ADUR关键久期折现因子表含价差(第二个)数据");
    }

    /**
     * 导入ADUR关键久期折现因子表含价差(第二个)数据
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:import')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AdurIssueMonthlyDiscountFactorDTO> util = new ExcelUtil<AdurIssueMonthlyDiscountFactorDTO>(AdurIssueMonthlyDiscountFactorDTO.class);
        List<AdurIssueMonthlyDiscountFactorDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = adurIssueMonthlyDiscountFactorService.importAdurIssueMonthlyDiscountFactorDto(dtoList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 获取期限数据列表
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:query')")
    @GetMapping("/termData/{id}")
    public Result getTermData(@PathVariable("id") Long id) {
        AdurIssueMonthlyDiscountFactorDTO dto = adurIssueMonthlyDiscountFactorService.selectAdurIssueMonthlyDiscountFactorDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        List<TermDataDTO> termDataList = TermDataUtil.parseJsonToTermDataList(dto.getMonthlyDiscountFactorSet());
        return Result.success(termDataList);
    }

    /**
     * 更新期限数据
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:edit')")
    @Log(title = "发行月度折现因子期限数据", businessType = BusinessType.UPDATE)
    @PutMapping("/termData/{id}")
    public Result updateTermData(@PathVariable("id") Long id, @RequestBody List<TermDataDTO> termDataList) {
        // 验证期限数据完整性
        if (!TermDataUtil.validateTermDataCompleteness(termDataList)) {
            return Result.error("期限数据不完整，必须包含term_0到term_600共601个期限");
        }

        // 获取原数据
        AdurIssueMonthlyDiscountFactorDTO dto = adurIssueMonthlyDiscountFactorService.selectAdurIssueMonthlyDiscountFactorDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        // 转换期限数据为JSON
        String jsonStr = TermDataUtil.convertTermDataListToJson(termDataList);
        dto.setMonthlyDiscountFactorSet(jsonStr);
        dto.setUpdateBy(getUsername());

        return toAjax(adurIssueMonthlyDiscountFactorService.updateAdurIssueMonthlyDiscountFactorDto(dto));
    }
}
