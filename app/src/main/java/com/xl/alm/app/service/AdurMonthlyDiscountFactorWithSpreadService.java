package com.xl.alm.app.service;

import com.xl.alm.app.dto.AdurMonthlyDiscountFactorWithSpreadDTO;
import com.xl.alm.app.query.AdurMonthlyDiscountFactorWithSpreadQuery;

import java.util.List;

/**
 * ADUR月度折现因子表含价差 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AdurMonthlyDiscountFactorWithSpreadService {

    /**
     * 查询ADUR月度折现因子表含价差列表
     *
     * @param adurMonthlyDiscountFactorWithSpreadQuery ADUR月度折现因子表含价差查询条件
     * @return ADUR月度折现因子表含价差列表
     */
    List<AdurMonthlyDiscountFactorWithSpreadDTO> selectAdurMonthlyDiscountFactorWithSpreadDtoList(AdurMonthlyDiscountFactorWithSpreadQuery adurMonthlyDiscountFactorWithSpreadQuery);

    /**
     * 用id查询ADUR月度折现因子表含价差
     *
     * @param id id
     * @return ADUR月度折现因子表含价差
     */
    AdurMonthlyDiscountFactorWithSpreadDTO selectAdurMonthlyDiscountFactorWithSpreadDtoById(Long id);

    /**
     * 根据账期、资产编号、久期类型和基点类型查询ADUR月度折现因子表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param durationType 久期类型
     * @param basisPointType 基点类型
     * @return ADUR月度折现因子表含价差
     */
    AdurMonthlyDiscountFactorWithSpreadDTO selectAdurMonthlyDiscountFactorWithSpreadDtoByCondition(String accountPeriod, String assetNumber, String durationType, String basisPointType);

    /**
     * 新增ADUR月度折现因子表含价差
     *
     * @param adurMonthlyDiscountFactorWithSpreadDTO ADUR月度折现因子表含价差
     * @return 结果
     */
    int insertAdurMonthlyDiscountFactorWithSpreadDto(AdurMonthlyDiscountFactorWithSpreadDTO adurMonthlyDiscountFactorWithSpreadDTO);

    /**
     * 批量插入ADUR月度折现因子表含价差数据
     *
     * @param adurMonthlyDiscountFactorWithSpreadDtoList ADUR月度折现因子表含价差列表
     * @return 影响行数
     */
    int batchInsertAdurMonthlyDiscountFactorWithSpreadDto(List<AdurMonthlyDiscountFactorWithSpreadDTO> adurMonthlyDiscountFactorWithSpreadDtoList);

    /**
     * 更新ADUR月度折现因子表含价差数据
     *
     * @param dto ADUR月度折现因子表含价差
     * @return 结果
     */
    int updateAdurMonthlyDiscountFactorWithSpreadDto(AdurMonthlyDiscountFactorWithSpreadDTO dto);

    /**
     * 删除指定id的ADUR月度折现因子表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteAdurMonthlyDiscountFactorWithSpreadDtoById(Long id);

    /**
     * 批量删除ADUR月度折现因子表含价差
     *
     * @param ids 需要删除的ADUR月度折现因子表含价差主键
     * @return 结果
     */
    int deleteAdurMonthlyDiscountFactorWithSpreadDtoByIds(Long[] ids);

    /**
     * 根据账期删除ADUR月度折现因子表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    int deleteAdurMonthlyDiscountFactorWithSpreadDtoByAccountPeriod(String accountPeriod);

    /**
     * 导入ADUR月度折现因子表含价差
     *
     * @param dtoList       ADUR月度折现因子表含价差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importAdurMonthlyDiscountFactorWithSpreadDto(List<AdurMonthlyDiscountFactorWithSpreadDTO> dtoList, Boolean updateSupport, String username);
}
