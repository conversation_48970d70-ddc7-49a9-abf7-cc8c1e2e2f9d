package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.MonthlyDiscountFactorDTO;
import com.xl.alm.app.entity.MonthlyDiscountFactorEntity;
import com.xl.alm.app.mapper.MonthlyDiscountFactorMapper;
import com.xl.alm.app.query.MonthlyDiscountFactorQuery;
import com.xl.alm.app.service.MonthlyDiscountFactorService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 月度折现因子表含价差 Service业务层处理
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class MonthlyDiscountFactorServiceImpl implements MonthlyDiscountFactorService {

    @Autowired
    private MonthlyDiscountFactorMapper monthlyDiscountFactorMapper;

    /**
     * 查询月度折现因子含价差列表
     *
     * @param monthlyDiscountFactorQuery 月度折现因子含价差查询条件
     * @return 月度折现因子含价差列表
     */
    @Override
    public List<MonthlyDiscountFactorDTO> selectMonthlyDiscountFactorDtoList(MonthlyDiscountFactorQuery monthlyDiscountFactorQuery) {
        List<MonthlyDiscountFactorEntity> entityList = monthlyDiscountFactorMapper.selectMonthlyDiscountFactorEntityList(monthlyDiscountFactorQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, MonthlyDiscountFactorDTO.class);
    }

    /**
     * 根据ID查询月度折现因子含价差
     *
     * @param id 主键ID
     * @return 月度折现因子含价差
     */
    @Override
    public MonthlyDiscountFactorDTO selectMonthlyDiscountFactorDtoById(Long id) {
        MonthlyDiscountFactorEntity entity = monthlyDiscountFactorMapper.selectMonthlyDiscountFactorEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, MonthlyDiscountFactorDTO.class);
    }

    /**
     * 新增月度折现因子含价差
     *
     * @param monthlyDiscountFactorDto 月度折现因子含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int insertMonthlyDiscountFactorDto(MonthlyDiscountFactorDTO monthlyDiscountFactorDto) {
        MonthlyDiscountFactorEntity entity = EntityDtoConvertUtil.convertToEntity(monthlyDiscountFactorDto, MonthlyDiscountFactorEntity.class);
        return monthlyDiscountFactorMapper.insertMonthlyDiscountFactorEntity(entity);
    }

    /**
     * 修改月度折现因子含价差
     *
     * @param monthlyDiscountFactorDto 月度折现因子含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int updateMonthlyDiscountFactorDto(MonthlyDiscountFactorDTO monthlyDiscountFactorDto) {
        MonthlyDiscountFactorEntity entity = EntityDtoConvertUtil.convertToEntity(monthlyDiscountFactorDto, MonthlyDiscountFactorEntity.class);
        return monthlyDiscountFactorMapper.updateMonthlyDiscountFactorEntity(entity);
    }

    /**
     * 批量删除月度折现因子含价差
     *
     * @param ids 需要删除的月度折现因子含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMonthlyDiscountFactorDtoByIds(Long[] ids) {
        return monthlyDiscountFactorMapper.deleteMonthlyDiscountFactorEntityByIds(ids);
    }

    /**
     * 删除月度折现因子含价差信息
     *
     * @param id 月度折现因子含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMonthlyDiscountFactorDtoById(Long id) {
        return monthlyDiscountFactorMapper.deleteMonthlyDiscountFactorEntityById(id);
    }

    /**
     * 导入月度折现因子含价差
     *
     * @param monthlyDiscountFactorList 月度折现因子含价差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importMonthlyDiscountFactorDto(List<MonthlyDiscountFactorDTO> monthlyDiscountFactorList, Boolean updateSupport, String operName) {
        if (StringUtils.isNull(monthlyDiscountFactorList) || monthlyDiscountFactorList.size() == 0) {
            throw new RuntimeException("导入月度折现因子含价差数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (MonthlyDiscountFactorDTO monthlyDiscountFactorDto : monthlyDiscountFactorList) {
            try {
                // 验证是否存在这个月度折现因子含价差
                MonthlyDiscountFactorEntity existEntity = monthlyDiscountFactorMapper.selectMonthlyDiscountFactorEntityByAccountPeriodAndAssetNumberAndDateType(
                    monthlyDiscountFactorDto.getAccountPeriod(), monthlyDiscountFactorDto.getAssetNumber(), monthlyDiscountFactorDto.getDateType());
                if (StringUtils.isNull(existEntity)) {
                    monthlyDiscountFactorDto.setCreateBy(operName);
                    this.insertMonthlyDiscountFactorDto(monthlyDiscountFactorDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + monthlyDiscountFactorDto.getAccountPeriod() + 
                        " 资产编号 " + monthlyDiscountFactorDto.getAssetNumber() + 
                        " 日期类型 " + monthlyDiscountFactorDto.getDateType() + " 导入成功");
                } else if (updateSupport) {
                    monthlyDiscountFactorDto.setUpdateBy(operName);
                    monthlyDiscountFactorDto.setId(existEntity.getId());
                    this.updateMonthlyDiscountFactorDto(monthlyDiscountFactorDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + monthlyDiscountFactorDto.getAccountPeriod() + 
                        " 资产编号 " + monthlyDiscountFactorDto.getAssetNumber() + 
                        " 日期类型 " + monthlyDiscountFactorDto.getDateType() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + monthlyDiscountFactorDto.getAccountPeriod() + 
                        " 资产编号 " + monthlyDiscountFactorDto.getAssetNumber() + 
                        " 日期类型 " + monthlyDiscountFactorDto.getDateType() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + monthlyDiscountFactorDto.getAccountPeriod() + 
                    " 资产编号 " + monthlyDiscountFactorDto.getAssetNumber() + 
                    " 日期类型 " + monthlyDiscountFactorDto.getDateType() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 根据账期删除月度折现因子含价差数据
     *
     * @param accountPeriod 账期
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteMonthlyDiscountFactorDtoByAccountPeriod(String accountPeriod) {
        return monthlyDiscountFactorMapper.deleteMonthlyDiscountFactorEntityByAccountPeriod(accountPeriod);
    }

    /**
     * 批量插入月度折现因子含价差数据
     *
     * @param monthlyDiscountFactorList 月度折现因子含价差列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertMonthlyDiscountFactorDto(List<MonthlyDiscountFactorDTO> monthlyDiscountFactorList) {
        if (monthlyDiscountFactorList == null || monthlyDiscountFactorList.isEmpty()) {
            return 0;
        }
        List<MonthlyDiscountFactorEntity> entityList = EntityDtoConvertUtil.convertToEntityList(monthlyDiscountFactorList, MonthlyDiscountFactorEntity.class);
        return monthlyDiscountFactorMapper.batchInsertMonthlyDiscountFactorEntity(entityList);
    }
}
