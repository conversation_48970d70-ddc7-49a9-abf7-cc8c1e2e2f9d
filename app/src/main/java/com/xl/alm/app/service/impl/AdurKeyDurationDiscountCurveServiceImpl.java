package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AdurKeyDurationDiscountCurveDTO;
import com.xl.alm.app.entity.AdurKeyDurationDiscountCurveEntity;
import com.xl.alm.app.mapper.AdurKeyDurationDiscountCurveMapper;
import com.xl.alm.app.query.AdurKeyDurationDiscountCurveQuery;
import com.xl.alm.app.service.AdurKeyDurationDiscountCurveService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ADUR关键久期折现曲线表含价差 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurKeyDurationDiscountCurveServiceImpl implements AdurKeyDurationDiscountCurveService {

    @Autowired
    private AdurKeyDurationDiscountCurveMapper adurKeyDurationDiscountCurveMapper;

    /**
     * 查询ADUR关键久期折现曲线表含价差列表
     *
     * @param adurKeyDurationDiscountCurveQuery ADUR关键久期折现曲线表含价差查询条件
     * @return ADUR关键久期折现曲线表含价差列表
     */
    @Override
    public List<AdurKeyDurationDiscountCurveDTO> selectAdurKeyDurationDiscountCurveDtoList(AdurKeyDurationDiscountCurveQuery adurKeyDurationDiscountCurveQuery) {
        List<AdurKeyDurationDiscountCurveEntity> entityList = adurKeyDurationDiscountCurveMapper.selectAdurKeyDurationDiscountCurveEntityList(adurKeyDurationDiscountCurveQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AdurKeyDurationDiscountCurveDTO.class);
    }

    /**
     * 用id查询ADUR关键久期折现曲线表含价差
     *
     * @param id id
     * @return ADUR关键久期折现曲线表含价差
     */
    @Override
    public AdurKeyDurationDiscountCurveDTO selectAdurKeyDurationDiscountCurveDtoById(Long id) {
        AdurKeyDurationDiscountCurveEntity entity = adurKeyDurationDiscountCurveMapper.selectAdurKeyDurationDiscountCurveEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationDiscountCurveDTO.class);
    }

    /**
     * 根据条件查询ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现曲线表含价差
     */
    @Override
    public AdurKeyDurationDiscountCurveDTO selectAdurKeyDurationDiscountCurveDtoByCondition(String accountPeriod, String assetNumber, String keyTerm, String stressDirection) {
        AdurKeyDurationDiscountCurveEntity entity = adurKeyDurationDiscountCurveMapper.selectAdurKeyDurationDiscountCurveEntityByCondition(accountPeriod, assetNumber, keyTerm, stressDirection);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationDiscountCurveDTO.class);
    }

    /**
     * 新增ADUR关键久期折现曲线表含价差
     *
     * @param adurKeyDurationDiscountCurveDTO ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdurKeyDurationDiscountCurveDto(AdurKeyDurationDiscountCurveDTO adurKeyDurationDiscountCurveDTO) {
        AdurKeyDurationDiscountCurveEntity entity = EntityDtoConvertUtil.convertToEntity(adurKeyDurationDiscountCurveDTO, AdurKeyDurationDiscountCurveEntity.class);
        return adurKeyDurationDiscountCurveMapper.insertAdurKeyDurationDiscountCurveEntity(entity);
    }

    /**
     * 批量插入ADUR关键久期折现曲线表含价差数据
     *
     * @param adurKeyDurationDiscountCurveDtoList ADUR关键久期折现曲线表含价差列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertAdurKeyDurationDiscountCurveDto(List<AdurKeyDurationDiscountCurveDTO> adurKeyDurationDiscountCurveDtoList) {
        if (adurKeyDurationDiscountCurveDtoList == null || adurKeyDurationDiscountCurveDtoList.isEmpty()) {
            return 0;
        }
        List<AdurKeyDurationDiscountCurveEntity> entityList = EntityDtoConvertUtil.convertToEntityList(adurKeyDurationDiscountCurveDtoList, AdurKeyDurationDiscountCurveEntity.class);
        return adurKeyDurationDiscountCurveMapper.batchInsertAdurKeyDurationDiscountCurveEntity(entityList);
    }

    /**
     * 更新ADUR关键久期折现曲线表含价差数据
     *
     * @param dto ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdurKeyDurationDiscountCurveDto(AdurKeyDurationDiscountCurveDTO dto) {
        AdurKeyDurationDiscountCurveEntity entity = EntityDtoConvertUtil.convertToEntity(dto, AdurKeyDurationDiscountCurveEntity.class);
        return adurKeyDurationDiscountCurveMapper.updateAdurKeyDurationDiscountCurveEntity(entity);
    }

    /**
     * 删除指定id的ADUR关键久期折现曲线表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationDiscountCurveDtoById(Long id) {
        return adurKeyDurationDiscountCurveMapper.deleteAdurKeyDurationDiscountCurveEntityById(id);
    }

    /**
     * 批量删除ADUR关键久期折现曲线表含价差
     *
     * @param ids 需要删除的ADUR关键久期折现曲线表含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationDiscountCurveDtoByIds(Long[] ids) {
        return adurKeyDurationDiscountCurveMapper.deleteAdurKeyDurationDiscountCurveEntityByIds(ids);
    }

    /**
     * 导入ADUR关键久期折现曲线表含价差
     *
     * @param dtoList       ADUR关键久期折现曲线表含价差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importAdurKeyDurationDiscountCurveDto(List<AdurKeyDurationDiscountCurveDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.size() == 0) {
            throw new RuntimeException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AdurKeyDurationDiscountCurveDTO dto : dtoList) {
            try {
                // 验证是否存在这个数据
                AdurKeyDurationDiscountCurveDTO existDto = this.selectAdurKeyDurationDiscountCurveDtoByCondition(
                    dto.getAccountPeriod(), dto.getAssetNumber(), dto.getKeyTerm(), dto.getStressDirection());
                if (StringUtils.isNull(existDto)) {
                    dto.setCreateBy(username);
                    this.insertAdurKeyDurationDiscountCurveDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 导入成功");
                } else if (updateSupport) {
                    dto.setId(existDto.getId());
                    dto.setUpdateBy(username);
                    this.updateAdurKeyDurationDiscountCurveDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 根据账期删除ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationDiscountCurveDtoByAccountPeriod(String accountPeriod) {
        return adurKeyDurationDiscountCurveMapper.deleteAdurKeyDurationDiscountCurveEntityByAccountPeriod(accountPeriod);
    }
}
