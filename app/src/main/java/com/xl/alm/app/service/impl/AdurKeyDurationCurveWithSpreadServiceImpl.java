package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AdurKeyDurationCurveWithSpreadDTO;
import com.xl.alm.app.entity.AdurKeyDurationCurveWithSpreadEntity;
import com.xl.alm.app.mapper.AdurKeyDurationCurveWithSpreadMapper;
import com.xl.alm.app.query.AdurKeyDurationCurveWithSpreadQuery;
import com.xl.alm.app.service.AdurKeyDurationCurveWithSpreadService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ADUR关键久期折现曲线表含价差 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurKeyDurationCurveWithSpreadServiceImpl implements AdurKeyDurationCurveWithSpreadService {

    @Autowired
    private AdurKeyDurationCurveWithSpreadMapper adurKeyDurationCurveWithSpreadMapper;

    /**
     * 查询ADUR关键久期折现曲线表含价差列表
     *
     * @param adurKeyDurationCurveWithSpreadQuery ADUR关键久期折现曲线表含价差查询条件
     * @return ADUR关键久期折现曲线表含价差列表
     */
    @Override
    public List<AdurKeyDurationCurveWithSpreadDTO> selectAdurKeyDurationCurveWithSpreadDtoList(AdurKeyDurationCurveWithSpreadQuery adurKeyDurationCurveWithSpreadQuery) {
        List<AdurKeyDurationCurveWithSpreadEntity> entityList = adurKeyDurationCurveWithSpreadMapper.selectAdurKeyDurationCurveWithSpreadEntityList(adurKeyDurationCurveWithSpreadQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AdurKeyDurationCurveWithSpreadDTO.class);
    }

    /**
     * 用id查询ADUR关键久期折现曲线表含价差
     *
     * @param id id
     * @return ADUR关键久期折现曲线表含价差
     */
    @Override
    public AdurKeyDurationCurveWithSpreadDTO selectAdurKeyDurationCurveWithSpreadDtoById(Long id) {
        AdurKeyDurationCurveWithSpreadEntity entity = adurKeyDurationCurveWithSpreadMapper.selectAdurKeyDurationCurveWithSpreadEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationCurveWithSpreadDTO.class);
    }

    /**
     * 根据账期、资产编号、久期类型、关键期限和压力方向查询ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param durationType 久期类型
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现曲线表含价差
     */
    @Override
    public AdurKeyDurationCurveWithSpreadDTO selectAdurKeyDurationCurveWithSpreadDtoByCondition(String accountPeriod, String assetNumber, String durationType, String keyTerm, String stressDirection) {
        AdurKeyDurationCurveWithSpreadEntity entity = adurKeyDurationCurveWithSpreadMapper.selectAdurKeyDurationCurveWithSpreadEntityByCondition(accountPeriod, assetNumber, durationType, keyTerm, stressDirection);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationCurveWithSpreadDTO.class);
    }

    /**
     * 新增ADUR关键久期折现曲线表含价差
     *
     * @param adurKeyDurationCurveWithSpreadDTO ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdurKeyDurationCurveWithSpreadDto(AdurKeyDurationCurveWithSpreadDTO adurKeyDurationCurveWithSpreadDTO) {
        AdurKeyDurationCurveWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(adurKeyDurationCurveWithSpreadDTO, AdurKeyDurationCurveWithSpreadEntity.class);
        return adurKeyDurationCurveWithSpreadMapper.insertAdurKeyDurationCurveWithSpreadEntity(entity);
    }

    /**
     * 批量插入ADUR关键久期折现曲线表含价差数据
     *
     * @param adurKeyDurationCurveWithSpreadDtoList ADUR关键久期折现曲线表含价差列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertAdurKeyDurationCurveWithSpreadDto(List<AdurKeyDurationCurveWithSpreadDTO> adurKeyDurationCurveWithSpreadDtoList) {
        if (adurKeyDurationCurveWithSpreadDtoList == null || adurKeyDurationCurveWithSpreadDtoList.isEmpty()) {
            return 0;
        }
        List<AdurKeyDurationCurveWithSpreadEntity> entityList = EntityDtoConvertUtil.convertToEntityList(adurKeyDurationCurveWithSpreadDtoList, AdurKeyDurationCurveWithSpreadEntity.class);
        return adurKeyDurationCurveWithSpreadMapper.batchInsertAdurKeyDurationCurveWithSpreadEntity(entityList);
    }

    /**
     * 更新ADUR关键久期折现曲线表含价差数据
     *
     * @param dto ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdurKeyDurationCurveWithSpreadDto(AdurKeyDurationCurveWithSpreadDTO dto) {
        AdurKeyDurationCurveWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(dto, AdurKeyDurationCurveWithSpreadEntity.class);
        return adurKeyDurationCurveWithSpreadMapper.updateAdurKeyDurationCurveWithSpreadEntity(entity);
    }

    /**
     * 删除指定id的ADUR关键久期折现曲线表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationCurveWithSpreadDtoById(Long id) {
        return adurKeyDurationCurveWithSpreadMapper.deleteAdurKeyDurationCurveWithSpreadEntityById(id);
    }

    /**
     * 批量删除ADUR关键久期折现曲线表含价差
     *
     * @param ids 需要删除的ADUR关键久期折现曲线表含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationCurveWithSpreadDtoByIds(Long[] ids) {
        return adurKeyDurationCurveWithSpreadMapper.deleteAdurKeyDurationCurveWithSpreadEntityByIds(ids);
    }

    /**
     * 根据账期删除ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationCurveWithSpreadDtoByAccountPeriod(String accountPeriod) {
        return adurKeyDurationCurveWithSpreadMapper.deleteAdurKeyDurationCurveWithSpreadEntityByAccountPeriod(accountPeriod);
    }

    /**
     * 导入ADUR关键久期折现曲线表含价差
     *
     * @param dtoList       ADUR关键久期折现曲线表含价差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importAdurKeyDurationCurveWithSpreadDto(List<AdurKeyDurationCurveWithSpreadDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.size() == 0) {
            throw new RuntimeException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AdurKeyDurationCurveWithSpreadDTO dto : dtoList) {
            try {
                // 验证是否存在这个数据
                AdurKeyDurationCurveWithSpreadDTO existDto = this.selectAdurKeyDurationCurveWithSpreadDtoByCondition(dto.getAccountPeriod(), dto.getAssetNumber(), dto.getDurationType(), dto.getKeyTerm(), dto.getStressDirection());
                if (StringUtils.isNull(existDto)) {
                    dto.setCreateBy(username);
                    this.insertAdurKeyDurationCurveWithSpreadDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 久期类型 " + dto.getDurationType() + " 关键期限 " + dto.getKeyTerm() + " 压力方向 " + dto.getStressDirection() + " 导入成功");
                } else if (updateSupport) {
                    dto.setUpdateBy(username);
                    dto.setId(existDto.getId());
                    this.updateAdurKeyDurationCurveWithSpreadDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 久期类型 " + dto.getDurationType() + " 关键期限 " + dto.getKeyTerm() + " 压力方向 " + dto.getStressDirection() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 久期类型 " + dto.getDurationType() + " 关键期限 " + dto.getKeyTerm() + " 压力方向 " + dto.getStressDirection() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 久期类型 " + dto.getDurationType() + " 关键期限 " + dto.getKeyTerm() + " 压力方向 " + dto.getStressDirection() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
