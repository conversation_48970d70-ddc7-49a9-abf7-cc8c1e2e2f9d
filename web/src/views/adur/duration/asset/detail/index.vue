<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountPeriod">
        <el-input
          v-model="queryParams.accountPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资产编号" prop="assetNumber">
        <el-input
          v-model="queryParams.assetNumber"
          placeholder="请输入资产编号"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户名称" prop="accountName">
        <el-select
          v-model="queryParams.accountName"
          placeholder="请选择账户名称"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.adur_account_name"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资产名称" prop="assetName">
        <el-input
          v-model="queryParams.assetName"
          placeholder="请输入资产名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="证券代码" prop="securityCode">
        <el-input
          v-model="queryParams.securityCode"
          placeholder="请输入证券代码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="付息方式" prop="paymentMethod">
        <el-select
          v-model="queryParams.paymentMethod"
          placeholder="请选择付息方式"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.adur_payment_method"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['adur:duration:asset:detail:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['adur:duration:asset:detail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['adur:duration:asset:detail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['adur:duration:asset:detail:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="durationAssetDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键ID" align="center" prop="id" v-if="false" />
      <el-table-column label="账期" align="center" prop="accountPeriod" />
      <el-table-column label="资产编号" align="center" prop="assetNumber" />
      <el-table-column label="账户名称" align="center" prop="accountName">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.adur_account_name" :value="scope.row.accountName"/>
        </template>
      </el-table-column>
      <el-table-column label="资产名称" align="center" prop="assetName" :show-overflow-tooltip="true" />
      <el-table-column label="证券代码" align="center" prop="securityCode" />
      <el-table-column label="资产小小类" align="center" prop="assetSubCategory" :show-overflow-tooltip="true" />
      <el-table-column label="持仓面值" align="center" prop="holdingFaceValue" />
      <el-table-column label="市值" align="center" prop="marketValue" />
      <el-table-column label="账面余额" align="center" prop="bookBalance" />
      <el-table-column label="账面价值" align="center" prop="bookValue" />
      <el-table-column label="票面利率" align="center" prop="couponRate" />
      <el-table-column label="付息方式" align="center" prop="paymentMethod">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.adur_payment_method" :value="scope.row.paymentMethod"/>
        </template>
      </el-table-column>
      <el-table-column label="折现曲线标识" align="center" prop="curveId" />
      <el-table-column label="调整起息日" align="center" prop="adjustedValueDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.adjustedValueDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="调整买入日" align="center" prop="adjustedPurchaseDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.adjustedPurchaseDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="调整到期日" align="center" prop="adjustedMaturityDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.adjustedMaturityDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['adur:duration:asset:detail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['adur:duration:asset:detail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改久期资产明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账期" prop="accountPeriod">
              <el-input v-model="form.accountPeriod" placeholder="请输入账期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产编号" prop="assetNumber">
              <el-input v-model="form.assetNumber" placeholder="请输入资产编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账户名称" prop="accountName">
              <el-select v-model="form.accountName" placeholder="请选择账户名称">
                <el-option
                  v-for="dict in dict.type.adur_account_name"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产名称" prop="assetName">
              <el-input v-model="form.assetName" placeholder="请输入资产名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="证券代码" prop="securityCode">
              <el-input v-model="form.securityCode" placeholder="请输入证券代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产小小类" prop="assetSubCategory">
              <el-input v-model="form.assetSubCategory" placeholder="请输入资产小小类" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="持仓面值" prop="holdingFaceValue">
              <el-input v-model="form.holdingFaceValue" placeholder="请输入持仓面值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="市值" prop="marketValue">
              <el-input v-model="form.marketValue" placeholder="请输入市值" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账面余额" prop="bookBalance">
              <el-input v-model="form.bookBalance" placeholder="请输入账面余额" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账面价值" prop="bookValue">
              <el-input v-model="form.bookValue" placeholder="请输入账面价值" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="票面利率" prop="couponRate">
              <el-input v-model="form.couponRate" placeholder="请输入票面利率" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付息方式" prop="paymentMethod">
              <el-select v-model="form.paymentMethod" placeholder="请选择付息方式">
                <el-option
                  v-for="dict in dict.type.adur_payment_method"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listDurationAssetDetail, getDurationAssetDetail, delDurationAssetDetail, addDurationAssetDetail, updateDurationAssetDetail } from "@/api/adur/durationAssetDetail";

export default {
  name: "DurationAssetDetail",
  dicts: ['adur_account_name', 'adur_payment_method'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 久期资产明细表格数据
      durationAssetDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountPeriod: null,
        assetNumber: null,
        accountName: null,
        assetName: null,
        securityCode: null,
        assetSubCategory: null,
        paymentMethod: null,
        curveId: null,
        issueSpreadCalcFlag: null,
        spreadDurationStatFlag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" }
        ],
        assetNumber: [
          { required: true, message: "资产编号不能为空", trigger: "blur" }
        ],
        accountName: [
          { required: true, message: "账户名称不能为空", trigger: "change" }
        ],
        assetName: [
          { required: true, message: "资产名称不能为空", trigger: "blur" }
        ],
        securityCode: [
          { required: true, message: "证券代码不能为空", trigger: "blur" }
        ],
        assetSubCategory: [
          { required: true, message: "资产小小类不能为空", trigger: "blur" }
        ],
        paymentMethod: [
          { required: true, message: "付息方式不能为空", trigger: "change" }
        ],
        curveId: [
          { required: true, message: "折现曲线标识不能为空", trigger: "blur" }
        ],
        adjustedValueDate: [
          { required: true, message: "调整起息日不能为空", trigger: "blur" }
        ],
        adjustedPurchaseDate: [
          { required: true, message: "调整买入日不能为空", trigger: "blur" }
        ],
        adjustedMaturityDate: [
          { required: true, message: "调整到期日不能为空", trigger: "blur" }
        ],
        issueSpreadCalcFlag: [
          { required: true, message: "发行时点价差计算标识不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询久期资产明细列表 */
    getList() {
      this.loading = true;
      listDurationAssetDetail(this.queryParams).then(response => {
        this.durationAssetDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountPeriod: null,
        assetNumber: null,
        accountName: null,
        assetName: null,
        securityCode: null,
        assetSubCategory: null,
        holdingFaceValue: null,
        marketValue: null,
        bookBalance: null,
        bookValue: null,
        couponRate: null,
        paymentMethod: null,
        curveId: null,
        adjustedValueDate: null,
        adjustedPurchaseDate: null,
        adjustedMaturityDate: null,
        issueSpreadCalcFlag: null,
        spreadDurationStatFlag: null,
        evalSpread: null,
        spreadDuration: null,
        bookValueSigma9: null,
        bookValueSigma17: null,
        bookValueSigma77: null,
        issuePresentValue: null,
        issueSpread: null,
        evalPresentValue: null,
        evalMaturityYield: null,
        assetModifiedDuration: null,
        evalPresentValuePlus50bp: null,
        evalPresentValueMinus50bp: null,
        assetEffectiveDuration: null,
        issueCashflowSet: null,
        evalCashflowSet: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加久期资产明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDurationAssetDetail(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改久期资产明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDurationAssetDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDurationAssetDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除久期资产明细编号为"' + ids + '"的数据项？').then(function() {
        return delDurationAssetDetail(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('adur/duration/asset/detail/export', {
        ...this.queryParams
      }, `久期资产明细_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
